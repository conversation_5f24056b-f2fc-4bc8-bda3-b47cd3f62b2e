"""
Complete demo showing the full workflow:
1. Original 3x3 mapping with new efficient order
2. Z interpolation with configurable offset
3. Detailed logging and results
"""

import sys
import os
import numpy as np
import time

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Z_Interpolation import ZInterpolation

class MockMappingAFWorker:
    """Mock version of MappingAFWorker for demonstration"""
    
    def __init__(self, grbl_start, grbl_end):
        self.grbl_start = grbl_start
        self.grbl_end = grbl_end
        self.results = {}
        
    def simulate_mapping_with_new_order(self):
        """Simulate the new efficient mapping order: 5→4→7→8→9→6→3→2→1"""
        
        print("="*80)
        print("SIMULATING MAPPING AF WITH NEW EFFICIENT ORDER")
        print("="*80)
        print(f"ROI: X=[{self.grbl_start[0]:.2f}, {self.grbl_end[0]:.2f}], Y=[{self.grbl_start[1]:.2f}, {self.grbl_end[1]:.2f}]")
        
        # Calculate 3x3 grid points
        x_points = np.linspace(self.grbl_start[0], self.grbl_end[0], 3)
        y_points = np.linspace(self.grbl_start[1], self.grbl_end[1], 3)
        
        print("Grid Layout:")
        print("1 2 3")
        print("4 5 6")
        print("7 8 9")
        print()
        
        # Define the efficient order mapping (grid position -> order)
        # MODIFIED: Changed from 1 Full AF + 8 Refine to 9 Full AF
        efficient_order = [
            (1, 1, 'full', 5),   # Center
            (1, 0, 'full', 4),   # Left middle (changed from refine to full)
            (2, 0, 'full', 7),   # Bottom left (changed from refine to full)
            (2, 1, 'full', 8),   # Bottom center (changed from refine to full)
            (2, 2, 'full', 9),   # Bottom right (changed from refine to full)
            (1, 2, 'full', 6),   # Right middle (changed from refine to full)
            (0, 2, 'full', 3),   # Top right (changed from refine to full)
            (0, 1, 'full', 2),   # Top center (changed from refine to full)
            (0, 0, 'full', 1)    # Top left (changed from refine to full)
        ]
        
        print("Processing Order: 5→4→7→8→9→6→3→2→1")
        print("-" * 50)
        
        # Simulate realistic Z values with some variation
        base_z = 10.05
        z_variations = [0.02, -0.01, -0.013, 0.007, 0.006, -0.013, 0.033, 0.02, 0.046]
        
        # Process points in efficient order
        for order_num, (i, j, _, grid_num) in enumerate(efficient_order, 1):  # af_type not needed since all are 'full' now
            x = x_points[j]
            y = y_points[i]
            z = base_z + z_variations[grid_num - 1]
            
            # Store result
            self.results[(x, y)] = z
            
            af_desc = "Full AF"  # All points now use Full AF
            print(f"  {order_num}. Grid {grid_num}: X={x:.3f}, Y={y:.3f} -> Z={z:.4f} [{af_desc}]")
            
            # Simulate processing time
            time.sleep(0.1)
        
        print(f"\nMapping complete! Total points: {len(self.results)}")
        return True
    
    def log_mapping_results(self):
        """Log the mapping results in the new format"""
        
        log = "\n" + "="*80 + "\n"
        log += " " * 25 + "MAPPING AF RESULTS\n"
        log += "="*80 + "\n"
        log += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        log += f"ROI Area (GRBL):\n"
        log += f"  - Start: X={self.grbl_start[0]:.3f}, Y={self.grbl_start[1]:.3f}\n"
        log += f"  - End:   X={self.grbl_end[0]:.3f}, Y={self.grbl_end[1]:.3f}\n"
        log += "-"*80 + "\n"
        log += "Original 3x3 Mapping Points (Z-position):\n"
        
        # Calculate grid points
        x_points = np.linspace(self.grbl_start[0], self.grbl_end[0], 3)
        y_points = np.linspace(self.grbl_start[1], self.grbl_end[1], 3)
        
        # Display in grid format
        for i in range(3):
            row_str = ""
            for j in range(3):
                x = x_points[j]
                y = y_points[i]
                z_val = self.results.get((x, y), float('nan'))
                grid_num = i * 3 + j + 1
                row_str += f"  {grid_num}:({x:.2f},{y:.2f})->Z={z_val:.4f} |"
            log += row_str[:-2] + "\n"

        log += "\nDetailed Point List:\n"
        for i in range(3):
            for j in range(3):
                x = x_points[j]
                y = y_points[i]
                z_val = self.results.get((x, y), float('nan'))
                grid_num = i * 3 + j + 1
                log += f"  Point {grid_num}: X={x:.3f}, Y={y:.3f}, Z={z_val:.4f}\n"

        log += f"\nTotal mapped points: {len(self.results)}\n"
        log += "="*80 + "\n"
        log += "NOTE: Use Z_Interpolation class to generate interpolated grid\n"
        log += "="*80 + "\n"
        
        print(log)

def complete_workflow_demo():
    """Complete workflow demonstration"""
    
    print("="*80)
    print("COMPLETE MAPPING AF + Z INTERPOLATION WORKFLOW")
    print("="*80)
    
    # Use realistic coordinates
    grbl_start = (36.61, 3.07)
    grbl_end = (42.23, 7.88)
    
    # Step 1: Simulate mapping with new order
    mapping_worker = MockMappingAFWorker(grbl_start, grbl_end)
    mapping_worker.simulate_mapping_with_new_order()
    
    # Step 2: Log mapping results
    mapping_worker.log_mapping_results()
    
    # Step 3: Create Z interpolation
    print("\n" + "="*80)
    print("CREATING Z INTERPOLATION")
    print("="*80)
    
    # Test different offset values
    offsets = [0.5, 0.25]
    
    for offset in offsets:
        print(f"\n" + "-"*60)
        print(f"Z INTERPOLATION WITH OFFSET = {offset}")
        print("-"*60)
        
        z_interp = ZInterpolation(grbl_start, grbl_end, mapping_worker.results, offset=offset)
        
        if z_interp.create_interpolation_grid():
            print("✓ Z Interpolation successful!")
            
            # Show detailed interpolation results
            print(z_interp.log_interpolation_results())
            
            # Save to file
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"interpolation_offset_{offset}_{timestamp}.txt"
            z_interp.save_interpolation_data(filename)
            
        else:
            print("❌ Z Interpolation failed!")
    
    print("\n" + "="*80)
    print("WORKFLOW COMPLETE")
    print("="*80)
    print("Summary:")
    print("✓ 3x3 Mapping with efficient order (5→4→7→8→9→6→3→2→1)")
    print("✓ ALL 9 points using Full AF (changed from 1 Full AF + 8 Refine)")
    print("✓ Z Interpolation with configurable offset")
    print("✓ Detailed logging and results")
    print("✓ Ready for high-precision auto-focus")

def comparison_demo():
    """Demo showing comparison between different offset values"""
    
    print("\n" + "="*80)
    print("OFFSET COMPARISON DEMO")
    print("="*80)
    
    grbl_start = (0.0, 0.0)
    grbl_end = (6.0, 6.0)
    
    # Simple test data
    mapping_results = {
        (0.0, 0.0): 10.0, (3.0, 0.0): 10.3, (6.0, 0.0): 10.6,
        (0.0, 3.0): 10.1, (3.0, 3.0): 10.4, (6.0, 3.0): 10.7,
        (0.0, 6.0): 10.2, (3.0, 6.0): 10.5, (6.0, 6.0): 10.8
    }
    
    offsets = [1.0, 0.5, 0.25, 0.1]
    
    print("Original 3x3 grid (6x6 area):")
    print("  (0,0)->10.0  (3,0)->10.3  (6,0)->10.6")
    print("  (0,3)->10.1  (3,3)->10.4  (6,3)->10.7")
    print("  (0,6)->10.2  (3,6)->10.5  (6,6)->10.8")
    print()
    
    for offset in offsets:
        z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results, offset=offset)
        
        if z_interp.create_interpolation_grid():
            all_points = z_interp.get_interpolated_grid_points()
            grid_size = f"{z_interp.x_points_count}x{z_interp.y_points_count}"
            print(f"Offset {offset:4.2f}: {grid_size:>6} grid = {len(all_points):3d} points")
        else:
            print(f"Offset {offset:4.2f}: FAILED")

if __name__ == "__main__":
    complete_workflow_demo()
    comparison_demo()
