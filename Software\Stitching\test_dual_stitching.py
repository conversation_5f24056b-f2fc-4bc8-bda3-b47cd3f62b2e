"""
Test script for dual stitching functionality
Tests the modified StitchingWorker with dual Z positioning
"""

import sys
import os
import numpy as np
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Auto_Focus.Z_Interpolation import ZInterpolation


class MockCamera:
    """Mock camera for testing"""
    def __init__(self):
        self.is_running = True
        self._last_numpy_frame = self._create_test_frame()
    
    def _create_test_frame(self):
        """Create a test frame"""
        # Create a simple RGB test pattern
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame[:, :, 0] = 100  # Red channel
        frame[:, :, 1] = 150  # Green channel  
        frame[:, :, 2] = 200  # Blue channel
        return frame


class MockGRBL:
    """Mock GRBL for testing"""
    def __init__(self):
        self.grbl = MockSerial()
        self.current_x = 0.0
        self.current_y = 0.0
        self.current_z = 10.0
        
    def move_to(self, x, y):
        """Mock move to XY position"""
        self.current_x = x
        self.current_y = y
        print(f"Mock GRBL: Moving to X={x:.3f}, Y={y:.3f}")
        
    def move_to_z(self, z):
        """Mock move to Z position"""
        self.current_z = z
        print(f"Mock GRBL: Moving Z to {z:.4f}")
        
    def get_current_position(self):
        """Mock get current position"""
        return self.current_x, self.current_y, self.current_z
        
    def get_status(self):
        """Mock get status"""
        return 'Idle'
        
    def wait_for_idle(self, timeout_seconds=30):
        """Mock wait for idle"""
        return True
        
    def stop_polling(self):
        """Mock stop polling"""
        print("Mock GRBL: Polling stopped")
        
    def start_polling(self):
        """Mock start polling"""
        print("Mock GRBL: Polling started")


class MockSerial:
    """Mock serial connection"""
    def __init__(self):
        self.is_open = True


def create_test_interpolation():
    """Create test interpolation data"""
    print("Creating test interpolation data...")
    
    # Define test area
    grbl_start = (0.0, 0.0)
    grbl_end = (3.0, 3.0)
    
    # Create 3x3 mapping results with center point having full AF
    # Grid layout:
    # 1 2 3
    # 4 5 6  <- Point 5 is center with full AF
    # 7 8 9
    mapping_results = {
        (0.0, 0.0): 10.1234,  # Point 1 (top-left)
        (1.5, 0.0): 10.2156,  # Point 2 (top-center)  
        (3.0, 0.0): 10.1987,  # Point 3 (top-right)
        (0.0, 1.5): 10.0876,  # Point 4 (middle-left)
        (1.5, 1.5): 10.1543,  # Point 5 (center) - This is the full AF result
        (3.0, 1.5): 10.2234,  # Point 6 (middle-right)
        (0.0, 3.0): 10.0654,  # Point 7 (bottom-left)
        (1.5, 3.0): 10.1321,  # Point 8 (bottom-center)
        (3.0, 3.0): 10.1876   # Point 9 (bottom-right)
    }
    
    print("Creating test interpolation...")
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)
    
    if z_interp.create_interpolation_grid():
        print("✓ Test interpolation created successfully!")
        print(f"Center Z value should be: {mapping_results[(1.5, 1.5)]:.4f}")
        return z_interp
    else:
        print("❌ Failed to create test interpolation")
        return None


def test_dual_stitching():
    """Test the dual stitching functionality"""
    print("\n" + "="*60)
    print("TESTING DUAL STITCHING FUNCTIONALITY")
    print("="*60)
    
    try:
        # Import StitchingWorker
        from StitchingWorker import StitchingWorker
        
        # Create mock objects
        mock_camera = MockCamera()
        mock_grbl = MockGRBL()
        test_interpolation = create_test_interpolation()
        
        if test_interpolation is None:
            print("❌ Cannot proceed without interpolation data")
            return False
        
        # Create test output folder
        test_folder = f"test_dual_stitching_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create StitchingWorker
        print("\nCreating StitchingWorker...")
        worker = StitchingWorker(
            camera=mock_camera,
            grbl=mock_grbl,
            z_interpolation=test_interpolation,
            output_folder=test_folder
        )
        
        # Connect signals for testing
        def on_progress(percent, message):
            print(f"Progress: {percent}% - {message}")
            
        def on_log_message(message):
            print(f"Log: {message}")
            
        def on_error(error_message):
            print(f"ERROR: {error_message}")
            
        def on_finished():
            print("✓ Stitching finished!")
            
        worker.progress.connect(on_progress)
        worker.log_message.connect(on_log_message)
        worker.error_occurred.connect(on_error)
        worker.finished.connect(on_finished)
        
        # Test preparation
        print("\nTesting preparation...")
        if worker.prepare_stitching():
            print("✓ Preparation successful!")
            print(f"Total runs: {worker.total_runs}")
            print(f"Center Z value: {worker.center_z_value}")
            print(f"Interpolated grid points: {len(worker.interpolated_grid_points)}")
            print(f"Fixed Z grid points: {len(worker.fixed_z_grid_points)}")
        else:
            print("❌ Preparation failed!")
            return False
        
        # Test a few points from each run to verify the setup
        print("\nTesting grid point setup...")
        if worker.interpolated_grid_points:
            x, y, z = worker.interpolated_grid_points[0]
            print(f"First interpolated point: X={x:.3f}, Y={y:.3f}, Z={z:.4f}")
            
        if worker.fixed_z_grid_points:
            x, y, z = worker.fixed_z_grid_points[0]
            print(f"First fixed Z point: X={x:.3f}, Y={y:.3f}, Z={z:.4f}")
            print(f"All fixed Z points should have Z={worker.center_z_value:.4f}")
        
        print("\n✓ Dual stitching test completed successfully!")
        print(f"Test output folder: {worker.output_folder}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Starting dual stitching test...")
    success = test_dual_stitching()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Tests failed!")
    
    input("\nPress Enter to exit...")
