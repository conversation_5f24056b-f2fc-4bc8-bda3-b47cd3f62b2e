"""
Test script untuk memverifikasi urutan Mapping AF yang baru
Urutan baru: 5→4→1→2→3→6→9→8→7
"""

import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_mapping_order():
    """Test urutan mapping AF yang baru"""
    print("="*60)
    print("TESTING MAPPING AF ORDER")
    print("="*60)
    
    # Define the new efficient order (same as in Mapping_AF.py)
    # MODIFIED: Changed from 1 Full AF + 8 Refine to 9 Full AF
    efficient_order = [
        (1, 1, 'full', 5),   # Center
        (1, 0, 'full', 4),   # Left middle (changed from refine to full)
        (2, 0, 'full', 7),   # Bottom left (changed from refine to full)
        (2, 1, 'full', 8),   # Bottom center (changed from refine to full)
        (2, 2, 'full', 9),   # Bottom right (changed from refine to full)
        (1, 2, 'full', 6),   # Right middle (changed from refine to full)
        (0, 2, 'full', 3),   # Top right (changed from refine to full)
        (0, 1, 'full', 2),   # Top center (changed from refine to full)
        (0, 0, 'full', 1)    # Top left (changed from refine to full)
    ]
    
    print("Grid Layout:")
    print("1 2 3")
    print("4 5 6")
    print("7 8 9")
    print()
    
    print("New Processing Order: 5→4→7→8→9→6→3→2→1 (ALL Full AF)")
    print("-" * 50)

    expected_order = [5, 4, 7, 8, 9, 6, 3, 2, 1]
    actual_order = []
    
    for order_num, (i, j, _, grid_num) in enumerate(efficient_order, 1):  # af_type not needed since all are 'full' now
        actual_order.append(grid_num)
        af_desc = "Full AF"  # All points now use Full AF
        print(f"  {order_num}. Grid {grid_num}: Position ({i},{j}) [{af_desc}]")
    
    print("-" * 50)
    print(f"Expected order: {expected_order}")
    print(f"Actual order:   {actual_order}")
    
    if actual_order == expected_order:
        print("✅ SUCCESS: Urutan mapping AF sudah benar!")
        return True
    else:
        print("❌ ERROR: Urutan mapping AF tidak sesuai!")
        return False

if __name__ == "__main__":
    success = test_mapping_order()
    
    if success:
        print("\n🎉 Test berhasil! Urutan mapping AF sudah diubah ke 5→4→7→8→9→6→3→2→1 (ALL Full AF)")
    else:
        print("\n❌ Test gagal!")
    
    input("\nPress Enter to exit...")
