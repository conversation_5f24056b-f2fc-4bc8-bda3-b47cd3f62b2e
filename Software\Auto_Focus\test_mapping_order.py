"""
Test script untuk memverifikasi urutan Mapping AF yang baru
Urutan baru: 5→4→1→2→3→6→9→8→7
"""

import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_mapping_order():
    """Test urutan mapping AF yang baru"""
    print("="*60)
    print("TESTING MAPPING AF ORDER")
    print("="*60)
    
    # Define the new efficient order (same as in Mapping_AF.py)
    efficient_order = [
        (1, 1, 'full', 5),   # Center
        (1, 0, 'refine', 4), # Left middle
        (0, 0, 'refine', 1), # Top left
        (0, 1, 'refine', 2), # Top center
        (0, 2, 'refine', 3), # Top right
        (1, 2, 'refine', 6), # Right middle
        (2, 2, 'refine', 9), # Bottom right
        (2, 1, 'refine', 8), # Bottom center
        (2, 0, 'refine', 7)  # Bottom left
    ]
    
    print("Grid Layout:")
    print("1 2 3")
    print("4 5 6")
    print("7 8 9")
    print()
    
    print("New Processing Order: 5→4→1→2→3→6→9→8→7")
    print("-" * 50)
    
    expected_order = [5, 4, 1, 2, 3, 6, 9, 8, 7]
    actual_order = []
    
    for order_num, (i, j, af_type, grid_num) in enumerate(efficient_order, 1):
        actual_order.append(grid_num)
        af_desc = "Full AF - Baseline" if af_type == 'full' else "Refine Scan"
        print(f"  {order_num}. Grid {grid_num}: Position ({i},{j}) [{af_desc}]")
    
    print("-" * 50)
    print(f"Expected order: {expected_order}")
    print(f"Actual order:   {actual_order}")
    
    if actual_order == expected_order:
        print("✅ SUCCESS: Urutan mapping AF sudah benar!")
        return True
    else:
        print("❌ ERROR: Urutan mapping AF tidak sesuai!")
        return False

if __name__ == "__main__":
    success = test_mapping_order()
    
    if success:
        print("\n🎉 Test berhasil! Urutan mapping AF sudah diubah ke 5→4→1→2→3→6→9→8→7")
    else:
        print("\n❌ Test gagal!")
    
    input("\nPress Enter to exit...")
