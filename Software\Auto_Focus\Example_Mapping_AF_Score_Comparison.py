"""
Contoh penggunaan Mapping AF dengan fitur perbandingan skor verifikasi vs koreksi
"""

from PyQt5.QtCore import QObject, pyqtSignal, QThread
from .Mapping_AF import MappingAFWorker
from .Z_Interpolation import ZInterpolation

class MappingAFExample(QObject):
    """
    Contoh implementasi Mapping AF dengan perbandingan skor
    """
    finished = pyqtSignal()
    log_message = pyqtSignal(str)
    
    def __init__(self, camera, grbl, parent=None):
        super().__init__(parent)
        self.camera = camera
        self.grbl = grbl
        self.mapping_worker = None
        self.results_original = None
        self.results_optimized = None
        self.score_summary = None
        
    def run_mapping_with_score_comparison(self, grbl_start, grbl_end, offset=0.5):
        """
        Menjalankan Mapping AF dengan fitur perbandingan skor
        
        Args:
            grbl_start: (x_start, y_start) tuple
            grbl_end: (x_end, y_end) tuple  
            offset: Grid offset (default 0.5)
        """
        self.log_message.emit("\n" + "="*60)
        self.log_message.emit("MAPPING AF DENGAN PERBANDINGAN SKOR")
        self.log_message.emit("="*60)
        
        # Buat mapping worker
        self.mapping_worker = MappingAFWorker(
            self.camera, 
            self.grbl, 
            grbl_start, 
            grbl_end, 
            offset
        )
        
        # Connect signals
        self.mapping_worker.finished.connect(self.on_mapping_finished)
        self.mapping_worker.log_message.connect(self.log_message.emit)
        
        # Jalankan mapping
        self.log_message.emit("Memulai proses Mapping AF...")
        self.mapping_worker.run()
        
    def on_mapping_finished(self):
        """
        Handler ketika mapping selesai - analisis hasil
        """
        try:
            self.log_message.emit("\n" + "="*60)
            self.log_message.emit("ANALISIS HASIL MAPPING AF")
            self.log_message.emit("="*60)
            
            # Dapatkan hasil asli dan optimized
            self.results_original = self.mapping_worker.results.copy()
            self.results_optimized = self.mapping_worker.get_optimized_results()
            self.score_summary = self.mapping_worker.get_score_comparison_summary()
            
            # Analisis perbandingan
            self.analyze_results()
            
            # Demonstrasi penggunaan untuk Z Interpolation
            self.demonstrate_z_interpolation()
            
            self.finished.emit()
            
        except Exception as e:
            self.log_message.emit(f"Error dalam analisis hasil: {e}")
            self.finished.emit()
    
    def analyze_results(self):
        """
        Menganalisis perbedaan antara hasil asli dan optimized
        """
        self.log_message.emit("\n1. PERBANDINGAN HASIL ASLI vs OPTIMIZED:")
        self.log_message.emit("-" * 50)
        
        total_points = len(self.results_original)
        changed_points = 0
        total_z_change = 0.0
        
        for (x, y) in self.results_original:
            z_original = self.results_original[(x, y)]
            z_optimized = self.results_optimized.get((x, y), z_original)
            
            z_diff = abs(z_optimized - z_original)
            if z_diff > 0.0001:  # Threshold untuk perubahan signifikan
                changed_points += 1
                total_z_change += z_diff
                self.log_message.emit(
                    f"  Point ({x:.2f}, {y:.2f}): "
                    f"Z {z_original:.4f} → {z_optimized:.4f} "
                    f"(Δ{z_optimized-z_original:+.4f})"
                )
        
        self.log_message.emit(f"\nRingkasan Perubahan:")
        self.log_message.emit(f"  - Total points: {total_points}")
        self.log_message.emit(f"  - Points berubah: {changed_points}")
        self.log_message.emit(f"  - Points tetap: {total_points - changed_points}")
        if changed_points > 0:
            avg_change = total_z_change / changed_points
            self.log_message.emit(f"  - Rata-rata perubahan Z: {avg_change:.4f} mm")
        
        # Analisis score summary jika ada
        if self.score_summary:
            self.log_message.emit("\n2. RINGKASAN PERBANDINGAN SKOR:")
            self.log_message.emit("-" * 50)
            self.log_message.emit(f"  - Total points dengan perbandingan: {self.score_summary['total_points']}")
            self.log_message.emit(f"  - Menggunakan koreksi: {self.score_summary['correction_used']}")
            self.log_message.emit(f"  - Menggunakan verifikasi: {self.score_summary['verification_used']}")
            
            # Analisis improvement
            improvements = [d['score_improvement'] for d in self.score_summary['details']]
            positive_improvements = [imp for imp in improvements if imp > 0]
            negative_improvements = [imp for imp in improvements if imp < 0]
            
            if positive_improvements:
                avg_positive = sum(positive_improvements) / len(positive_improvements)
                self.log_message.emit(f"  - Rata-rata peningkatan skor (koreksi lebih baik): {avg_positive:.2f}")
            
            if negative_improvements:
                avg_negative = sum(negative_improvements) / len(negative_improvements)
                self.log_message.emit(f"  - Rata-rata penurunan skor (verifikasi lebih baik): {avg_negative:.2f}")
    
    def demonstrate_z_interpolation(self):
        """
        Demonstrasi penggunaan hasil optimized untuk Z Interpolation
        """
        self.log_message.emit("\n3. DEMONSTRASI Z INTERPOLATION:")
        self.log_message.emit("-" * 50)
        
        try:
            # Gunakan hasil optimized untuk interpolation
            grbl_start = self.mapping_worker.grbl_start
            grbl_end = self.mapping_worker.grbl_end
            
            self.log_message.emit("Membuat Z Interpolation dengan hasil OPTIMIZED...")
            z_interp_optimized = ZInterpolation(grbl_start, grbl_end, self.results_optimized)
            
            if z_interp_optimized.create_interpolation_grid():
                self.log_message.emit("✓ Z Interpolation berhasil dibuat dengan hasil optimized")
                
                # Bandingkan dengan interpolation menggunakan hasil asli
                self.log_message.emit("Membuat Z Interpolation dengan hasil ASLI untuk perbandingan...")
                z_interp_original = ZInterpolation(grbl_start, grbl_end, self.results_original)
                
                if z_interp_original.create_interpolation_grid():
                    self.log_message.emit("✓ Z Interpolation berhasil dibuat dengan hasil asli")
                    
                    # Contoh perbandingan di beberapa titik
                    test_positions = [
                        (grbl_start[0] + 0.5, grbl_start[1] + 0.5),
                        (grbl_start[0] + 1.5, grbl_start[1] + 1.5),
                        (grbl_start[0] + 2.5, grbl_start[1] + 2.5)
                    ]
                    
                    self.log_message.emit("\nPerbandingan interpolasi di beberapa titik test:")
                    for i, (test_x, test_y) in enumerate(test_positions, 1):
                        z_orig = z_interp_original.get_z_at_position(test_x, test_y)
                        z_opt = z_interp_optimized.get_z_at_position(test_x, test_y)
                        
                        if z_orig is not None and z_opt is not None:
                            diff = z_opt - z_orig
                            self.log_message.emit(
                                f"  Test {i} ({test_x:.2f}, {test_y:.2f}): "
                                f"Asli={z_orig:.4f}, Optimized={z_opt:.4f}, "
                                f"Δ{diff:+.4f}"
                            )
                else:
                    self.log_message.emit("❌ Gagal membuat Z Interpolation dengan hasil asli")
            else:
                self.log_message.emit("❌ Gagal membuat Z Interpolation dengan hasil optimized")
                
        except Exception as e:
            self.log_message.emit(f"Error dalam demonstrasi Z Interpolation: {e}")
    
    def save_comparison_report(self, filename="mapping_af_comparison_report.txt"):
        """
        Menyimpan laporan perbandingan ke file
        """
        try:
            with open(filename, 'w') as f:
                f.write("MAPPING AF SCORE COMPARISON REPORT\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("HASIL ASLI (Z dari sapuan):\n")
                for (x, y), z in self.results_original.items():
                    f.write(f"  ({x:.2f}, {y:.2f}) -> Z={z:.4f}\n")
                
                f.write("\nHASIL OPTIMIZED (Z terpilih berdasarkan skor):\n")
                for (x, y), z in self.results_optimized.items():
                    f.write(f"  ({x:.2f}, {y:.2f}) -> Z={z:.4f}\n")
                
                if self.score_summary:
                    f.write(f"\nSCORE COMPARISON SUMMARY:\n")
                    f.write(f"Total points: {self.score_summary['total_points']}\n")
                    f.write(f"Correction used: {self.score_summary['correction_used']}\n")
                    f.write(f"Verification used: {self.score_summary['verification_used']}\n")
                    
                    f.write("\nDetailed comparison:\n")
                    for detail in self.score_summary['details']:
                        pos = detail['position']
                        f.write(
                            f"  ({pos[0]:.2f}, {pos[1]:.2f}): {detail['chosen']} "
                            f"(Verif:{detail['verification_score']:.2f} vs "
                            f"Koreksi:{detail['correction_score']:.2f}, "
                            f"Improvement:{detail['score_improvement']:+.2f})\n"
                        )
            
            self.log_message.emit(f"\n✓ Laporan perbandingan disimpan ke: {filename}")
            
        except Exception as e:
            self.log_message.emit(f"❌ Gagal menyimpan laporan: {e}")


# Contoh penggunaan
if __name__ == "__main__":
    # Ini adalah contoh penggunaan, dalam implementasi nyata
    # camera dan grbl harus sudah diinisialisasi
    
    # example = MappingAFExample(camera, grbl)
    # example.log_message.connect(print)  # Print log ke console
    # example.run_mapping_with_score_comparison(
    #     grbl_start=(32.3, 3.63),
    #     grbl_end=(39.7, 8.70),
    #     offset=0.5
    # )
    
    print("Contoh penggunaan Mapping AF dengan perbandingan skor")
    print("Lihat kode di atas untuk implementasi lengkap")
