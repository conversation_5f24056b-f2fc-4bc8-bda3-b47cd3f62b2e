"""
Contoh konfigurasi parameter untuk Mapping AF dengan timing yang diperbaiki
"""

# Parameter Default (untuk refine manual)
DEFAULT_REFINE_PARAMS = {
    "autofocus.refine.trend_scan_feedrate": 2.0,      # mm/min
    "autofocus.refine.fine_scan_feedrate": 1.0,       # mm/min
    "autofocus.refine.trend_scan_distance": 0.5,      # mm
    "autofocus.refine.fine_scan_range": 0.5,          # mm
    "autofocus.refine.early_stop_drop_pct": 0.30,     # 30%
    "autofocus.refine.min_frames_before_early_stop": 15,
    "autofocus.refine.min_decrease_window": 6,
    "autofocus.refine.patience_limit": 2,
    "autofocus.stabilization_sleep_ms": 80.0,         # ms
    "autofocus.warmup_ms": 80.0,                      # ms
}

# Parameter untuk Mapping AF (lebih stabil, lebih lambat)
MAPPING_REFINE_PARAMS = {
    # Kecepatan diperlambat untuk akurasi
    "autofocus.refine.trend_scan_feedrate": 0.8,      # 60% lebih lambat
    "autofocus.refine.fine_scan_feedrate": 0.5,       # 50% lebih lambat
    
    # Jarak scan diperkecil untuk presisi
    "autofocus.refine.trend_scan_distance": 0.3,      # 40% lebih kecil
    "autofocus.refine.fine_scan_range": 0.3,          # 40% lebih kecil
    
    # Early stop lebih konservatif
    "autofocus.refine.early_stop_drop_pct": 0.15,     # 15% (lebih ketat)
    "autofocus.refine.min_frames_before_early_stop": 20,  # Lebih banyak data
    "autofocus.refine.min_decrease_window": 8,        # Window lebih besar
    "autofocus.refine.patience_limit": 3,             # Lebih sabar
    
    # Stabilisasi diperpanjang
    "autofocus.stabilization_sleep_ms": 150.0,       # Hampir 2x lebih lama
    "autofocus.warmup_ms": 120.0,                     # 50% lebih lama
}

# Parameter untuk Mapping AF (alternatif - lebih cepat tapi tetap stabil)
MAPPING_REFINE_PARAMS_FAST = {
    # Kecepatan sedang
    "autofocus.refine.trend_scan_feedrate": 1.2,      # 40% lebih lambat dari default
    "autofocus.refine.fine_scan_feedrate": 0.7,       # 30% lebih lambat dari default
    
    # Jarak scan sedikit diperkecil
    "autofocus.refine.trend_scan_distance": 0.4,      # 20% lebih kecil
    "autofocus.refine.fine_scan_range": 0.4,          # 20% lebih kecil
    
    # Early stop moderat
    "autofocus.refine.early_stop_drop_pct": 0.20,     # 20%
    "autofocus.refine.min_frames_before_early_stop": 18,
    "autofocus.refine.min_decrease_window": 7,
    "autofocus.refine.patience_limit": 3,
    
    # Stabilisasi sedang
    "autofocus.stabilization_sleep_ms": 120.0,
    "autofocus.warmup_ms": 100.0,
}

# Parameter untuk Mapping AF (ultra presisi - sangat lambat tapi sangat akurat)
MAPPING_REFINE_PARAMS_PRECISION = {
    # Kecepatan sangat lambat
    "autofocus.refine.trend_scan_feedrate": 0.5,      # 75% lebih lambat
    "autofocus.refine.fine_scan_feedrate": 0.3,       # 70% lebih lambat
    
    # Jarak scan sangat kecil
    "autofocus.refine.trend_scan_distance": 0.2,      # 60% lebih kecil
    "autofocus.refine.fine_scan_range": 0.2,          # 60% lebih kecil
    
    # Early stop sangat konservatif
    "autofocus.refine.early_stop_drop_pct": 0.10,     # 10% (sangat ketat)
    "autofocus.refine.min_frames_before_early_stop": 25,  # Banyak data
    "autofocus.refine.min_decrease_window": 10,       # Window besar
    "autofocus.refine.patience_limit": 4,             # Sangat sabar
    
    # Stabilisasi maksimal
    "autofocus.stabilization_sleep_ms": 200.0,
    "autofocus.warmup_ms": 150.0,
}

# Timing delays untuk mapping
MAPPING_TIMING_CONFIG = {
    "pre_refine_stabilization": 1.0,      # detik sebelum refine AF
    "post_refine_delay": 2.0,             # detik setelah refine AF
    "post_full_af_delay": 0.5,            # detik setelah full AF
    "grbl_idle_check_delay": 0.5,         # detik setelah wait_for_idle()
}

# Contoh penggunaan dalam kode
def apply_mapping_config(config_type="standard"):
    """
    Menerapkan konfigurasi mapping berdasarkan tipe yang dipilih
    
    Args:
        config_type: "standard", "fast", "precision"
    """
    from configuration import config
    
    if config_type == "standard":
        params = MAPPING_REFINE_PARAMS
    elif config_type == "fast":
        params = MAPPING_REFINE_PARAMS_FAST
    elif config_type == "precision":
        params = MAPPING_REFINE_PARAMS_PRECISION
    else:
        raise ValueError(f"Unknown config type: {config_type}")
    
    print(f"Applying {config_type} mapping configuration...")
    for key, value in params.items():
        config.set(key, value)
        print(f"  {key}: {value}")

def restore_default_config():
    """
    Mengembalikan konfigurasi ke default
    """
    from configuration import config
    
    print("Restoring default configuration...")
    for key, value in DEFAULT_REFINE_PARAMS.items():
        config.set(key, value)
        print(f"  {key}: {value}")

# Contoh penggunaan dalam Mapping AF
class MappingAFWithCustomConfig:
    def __init__(self, camera, grbl, config_type="standard"):
        self.camera = camera
        self.grbl = grbl
        self.config_type = config_type
        self.original_config = None
    
    def start_mapping(self):
        """
        Memulai mapping dengan konfigurasi khusus
        """
        # Simpan konfigurasi asli
        self.original_config = self._backup_current_config()
        
        # Terapkan konfigurasi mapping
        apply_mapping_config(self.config_type)
        
        # Jalankan mapping
        # ... kode mapping AF ...
    
    def finish_mapping(self):
        """
        Menyelesaikan mapping dan mengembalikan konfigurasi
        """
        # Kembalikan konfigurasi asli
        if self.original_config:
            self._restore_config(self.original_config)
        else:
            restore_default_config()
    
    def _backup_current_config(self):
        """
        Backup konfigurasi saat ini
        """
        from configuration import config
        
        backup = {}
        for key in DEFAULT_REFINE_PARAMS.keys():
            backup[key] = config.get(key, DEFAULT_REFINE_PARAMS[key])
        return backup
    
    def _restore_config(self, backup_config):
        """
        Restore konfigurasi dari backup
        """
        from configuration import config
        
        for key, value in backup_config.items():
            config.set(key, value)

# Analisis performa untuk setiap konfigurasi
PERFORMANCE_ANALYSIS = {
    "standard": {
        "speed": "Lambat (60% dari default)",
        "accuracy": "Tinggi",
        "stability": "Sangat Stabil",
        "use_case": "Mapping rutin, keseimbangan speed vs accuracy",
        "estimated_time_per_point": "45-60 detik",
        "recommended_for": "Penggunaan umum mapping AF"
    },
    
    "fast": {
        "speed": "Sedang (70% dari default)", 
        "accuracy": "Baik",
        "stability": "Stabil",
        "use_case": "Mapping cepat dengan akurasi cukup",
        "estimated_time_per_point": "35-45 detik",
        "recommended_for": "Mapping preview atau testing"
    },
    
    "precision": {
        "speed": "Sangat Lambat (40% dari default)",
        "accuracy": "Sangat Tinggi",
        "stability": "Ultra Stabil", 
        "use_case": "Mapping presisi tinggi untuk aplikasi kritis",
        "estimated_time_per_point": "60-90 detik",
        "recommended_for": "Mapping final untuk produksi atau kalibrasi"
    }
}

def print_performance_analysis():
    """
    Menampilkan analisis performa untuk setiap konfigurasi
    """
    print("\n" + "="*60)
    print("ANALISIS PERFORMA KONFIGURASI MAPPING AF")
    print("="*60)
    
    for config_type, analysis in PERFORMANCE_ANALYSIS.items():
        print(f"\n{config_type.upper()} Configuration:")
        print("-" * 30)
        for key, value in analysis.items():
            print(f"  {key.replace('_', ' ').title()}: {value}")

if __name__ == "__main__":
    print("Mapping AF Configuration Examples")
    print("="*40)
    
    print("\nAvailable configurations:")
    for config_type in ["standard", "fast", "precision"]:
        print(f"- {config_type}")
    
    print_performance_analysis()
    
    print("\nUsage example:")
    print("apply_mapping_config('standard')  # Apply standard config")
    print("# ... run mapping AF ...")
    print("restore_default_config()          # Restore defaults")
